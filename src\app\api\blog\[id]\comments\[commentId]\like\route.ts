import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database-service';
import { z } from 'zod';

// 点赞请求验证schema
const likeCommentSchema = z.object({
  userId: z.string().optional(),
});

// 点赞/取消点赞评论
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string; commentId: string } }
) {
  try {
    const { commentId } = params;
    const body = await request.json();
    
    // 验证请求数据
    const validation = likeCommentSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data' },
        { status: 400 }
      );
    }

    const { userId } = validation.data;

    // 验证评论是否存在
    const comment = await DatabaseService.getCommentById(commentId);
    if (!comment) {
      return NextResponse.json(
        { success: false, error: 'Comment not found' },
        { status: 404 }
      );
    }

    // 获取客户端IP地址（用于游客点赞）
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    '127.0.0.1';

    // 检查是否已经点赞
    const existingLike = await DatabaseService.getCommentLike(commentId, userId, clientIP);
    
    let isLiked: boolean;
    let likeCount: number;

    if (existingLike) {
      // 取消点赞
      await DatabaseService.removeCommentLike(commentId, userId, clientIP);
      isLiked = false;
    } else {
      // 添加点赞
      await DatabaseService.addCommentLike(commentId, userId, clientIP);
      isLiked = true;
    }

    // 获取最新的点赞数
    likeCount = await DatabaseService.getCommentLikeCount(commentId);

    return NextResponse.json({
      success: true,
      data: {
        isLiked,
        likeCount,
      },
    });

  } catch (error) {
    console.error('Error toggling comment like:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to toggle comment like' },
      { status: 500 }
    );
  }
}