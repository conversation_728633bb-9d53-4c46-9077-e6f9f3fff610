'use client';

import React, { useState, useEffect } from 'react';
import { MessageCircle, Send, Reply, ThumbsUp, Flag, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import { showToast } from '@/components/ui/toast';
import { Comment } from '@/types';

interface CommentsProps {
  postId: string;
  initialComments?: Comment[];
  userId?: string;
  className?: string;
  allowGuests?: boolean;
}

export function Comments({
  postId,
  initialComments = [],
  userId,
  className,
  allowGuests = true,
}: CommentsProps) {
  const t = useTranslations('blog');
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [newComment, setNewComment] = useState('');
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [isLoading, setIsLoading] = useState(initialComments.length === 0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [guestName, setGuestName] = useState('');
  const [guestEmail, setGuestEmail] = useState('');
  const [newCommentId, setNewCommentId] = useState<string | null>(null);

  // 加载评论 - 只有在没有初始数据时才客户端加载
  useEffect(() => {
    if (initialComments.length === 0) {
      loadComments();
    }
  }, [postId, initialComments.length]);

  const loadComments = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (userId) {
        params.set('userId', userId);
      }
      
      const response = await fetch(`/api/blog/${postId}/comments?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setComments(data.data);
      }
    } catch (error) {
      console.error('Failed to load comments:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const submitComment = async (content: string, parentId?: string) => {
    if (!content.trim()) return;
    
    if (!userId && !allowGuests) {
      alert('请先登录后再发表评论');
      return;
    }

    if (!userId && allowGuests && (!guestName.trim() || !guestEmail.trim())) {
      alert('请填写姓名和邮箱');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/blog/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: content.trim(),
          parentId,
          userId,
          guestName: !userId ? guestName : undefined,
          guestEmail: !userId ? guestEmail : undefined,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        // 显示成功提示
        const isGuest = !userId;
        const successMessage = isGuest 
          ? '评论发表成功！感谢您的参与！' 
          : '评论发表成功！';
        
        // 显示高级简约的成功提示
        showToast({
          message: successMessage,
          type: 'success',
          duration: 3000,
          position: 'top-center'
        });
        
        // 乐观更新：立即添加新评论到列表
        const newComment: Comment = {
          id: data.data.id,
          content: content.trim(),
          userId: userId || undefined,
          userName: userId ? '当前用户' : (guestName || '匿名用户'),
          userAvatar: undefined,
          postId: postId,
          parentId: parentId || undefined,
          isApproved: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          guestName: !userId ? guestName : undefined,
          guestEmail: !userId ? guestEmail : undefined,
          likeCount: 0,
          isLiked: false,
        };

        // 更新评论列表状态
        if (parentId) {
          // 如果是回复，添加到对应的父评论下
          setComments(prevComments => 
            addReplyToCommentTree(prevComments, parentId, newComment)
          );
        } else {
          // 如果是主评论，直接添加到列表末尾
          setComments(prevComments => [...prevComments, newComment]);
        }
        
        // 设置新评论ID用于高亮显示
        setNewCommentId(newComment.id);
        
        // 平滑滚动到新评论
        setTimeout(() => {
          const newCommentElement = document.querySelector(`[data-comment-id="${newComment.id}"]`);
          if (newCommentElement) {
            newCommentElement.scrollIntoView({ 
              behavior: 'smooth', 
              block: 'center' 
            });
          }
        }, 100);
        
        // 3秒后移除高亮效果
        setTimeout(() => {
          setNewCommentId(null);
        }, 3000);
        
        // 清空表单
        if (parentId) {
          setReplyContent('');
          setReplyTo(null);
        } else {
          setNewComment('');
        }
        
        if (!userId) {
          setGuestName('');
          setGuestEmail('');
        }
      } else {
        // 显示具体的错误信息
        const errorMessage = data.error || '发表评论失败';
        showToast({
          message: errorMessage,
          type: 'error',
          duration: 4000,
          position: 'top-center'
        });
        
        // 如果是邮箱验证错误，聚焦到邮箱输入框
        if (data.field === 'guestEmail') {
          const emailInput = document.querySelector('input[type="email"]') as HTMLInputElement;
          if (emailInput) {
            emailInput.focus();
            emailInput.select();
          }
        }
      }
    } catch (error) {
      console.error('Failed to submit comment:', error);
      showToast({
        message: '网络错误，请检查网络连接后重试',
        type: 'error',
        duration: 4000,
        position: 'top-center'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: string) => {
    // 先进行乐观更新，立即反馈给用户
    const currentComment = findCommentInTree(comments, commentId);
    if (!currentComment) return;
    
    const newIsLiked = !currentComment.isLiked;
    const newLikeCount = newIsLiked 
      ? (currentComment.likeCount || 0) + 1 
      : Math.max((currentComment.likeCount || 0) - 1, 0);
    
    // 立即更新UI
    setComments(prevComments => 
      updateCommentInTree(prevComments, commentId, {
        likeCount: newLikeCount,
        isLiked: newIsLiked,
      })
    );

    try {
      const response = await fetch(`/api/blog/${postId}/comments/${commentId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      const data = await response.json();
      
      if (data.success) {
        // 用服务器返回的准确数据更新
        setComments(prevComments => 
          updateCommentInTree(prevComments, commentId, {
            likeCount: data.data.likeCount,
            isLiked: data.data.isLiked,
          })
        );
      } else {
        // 如果后端出错，回滚UI状态
        setComments(prevComments => 
          updateCommentInTree(prevComments, commentId, {
            likeCount: currentComment.likeCount || 0,
            isLiked: currentComment.isLiked || false,
          })
        );
        
        // 如果是数据库表不存在的错误，使用本地存储
        if (data.error && data.error.includes('does not exist')) {
          handleLikeCommentLocally(commentId, newIsLiked, newLikeCount);
        } else {
          showToast({
            message: data.error || '点赞失败，请重试',
            type: 'error',
            duration: 3000,
            position: 'top-center'
          });
        }
      }
    } catch (error) {
      console.error('Failed to like comment:', error);
      
      // 网络错误时回滚状态
      setComments(prevComments => 
        updateCommentInTree(prevComments, commentId, {
          likeCount: currentComment.likeCount || 0,
          isLiked: currentComment.isLiked || false,
        })
      );
      
      // 使用本地存储作为备选方案
      handleLikeCommentLocally(commentId, newIsLiked, newLikeCount);
    }
  };

  // 本地存储的点赞功能（临时方案）
  const handleLikeCommentLocally = (commentId: string, isLiked: boolean, likeCount: number) => {
    const likedComments = JSON.parse(localStorage.getItem('likedComments') || '{}');
    
    if (isLiked) {
      likedComments[commentId] = { isLiked: true, timestamp: Date.now() };
    } else {
      delete likedComments[commentId];
    }
    
    localStorage.setItem('likedComments', JSON.stringify(likedComments));
    
    // 更新UI状态
    setComments(prevComments => 
      updateCommentInTree(prevComments, commentId, {
        likeCount,
        isLiked,
      })
    );
  };

  // 查找评论的辅助函数
  const findCommentInTree = (comments: Comment[], commentId: string): Comment | null => {
    for (const comment of comments) {
      if (comment.id === commentId) {
        return comment;
      }
      if (comment.replies) {
        const found = findCommentInTree(comment.replies, commentId);
        if (found) return found;
      }
    }
    return null;
  };

  const updateCommentInTree = (comments: Comment[], commentId: string, updates: Partial<Comment>): Comment[] => {
    return comments.map(comment => {
      if (comment.id === commentId) {
        return { ...comment, ...updates };
      }
      if (comment.replies) {
        return {
          ...comment,
          replies: updateCommentInTree(comment.replies, commentId, updates),
        };
      }
      return comment;
    });
  };

  const addReplyToCommentTree = (comments: Comment[], parentId: string, newReply: Comment): Comment[] => {
    return comments.map(comment => {
      if (comment.id === parentId) {
        return {
          ...comment,
          replies: [...(comment.replies || []), newReply],
        };
      }
      if (comment.replies) {
        return {
          ...comment,
          replies: addReplyToCommentTree(comment.replies, parentId, newReply),
        };
      }
      return comment;
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}小时前`;
    } else if (diffInHours < 24 * 7) {
      return `${Math.floor(diffInHours / 24)}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const renderComment = (comment: Comment, isReply = false) => {
    const isNewComment = newCommentId === comment.id;
    
    return (
      <div
        key={comment.id}
        data-comment-id={comment.id}
        className={cn(
          'border-b border-gray-100 dark:border-gray-800 pb-4 mb-4 transition-all duration-1000',
          isReply && 'ml-8 border-l-2 border-gray-200 dark:border-gray-700 pl-4',
          isNewComment && 'bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800 rounded-lg p-4 -m-4 mb-0'
        )}
      >
    
      <div className="flex items-start gap-3">
        {/* 用户头像 */}
        <div className="flex-shrink-0">
          {comment.userAvatar ? (
            <img
              src={comment.userAvatar}
              alt={comment.userName || '用户'}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className={cn(
              "w-8 h-8 rounded-full flex items-center justify-center",
              comment.userId 
                ? "bg-blue-100 dark:bg-blue-900/20" // 登录用户
                : "bg-gray-300 dark:bg-gray-600"    // 游客
            )}>
              <User className={cn(
                "w-4 h-4",
                comment.userId 
                  ? "text-blue-600 dark:text-blue-400" // 登录用户
                  : "text-gray-600 dark:text-gray-400" // 游客
              )} />
            </div>
          )}
        </div>

        {/* 评论内容 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-gray-900 dark:text-gray-100">
              {comment.userName || '匿名用户'}
            </span>
            {!comment.userId && (
              <span className="text-xs px-2 py-0.5 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 rounded">
                游客
              </span>
            )}
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {formatDate(comment.createdAt)}
            </span>
          </div>
          
          <p className="text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">
            {comment.content}
          </p>

          {/* 评论操作 */}
          <div className="flex items-center gap-4 text-sm">
            <button
              onClick={() => handleLikeComment(comment.id)}
              className={cn(
                'flex items-center gap-1 transition-colors',
                comment.isLiked
                  ? 'text-red-500'
                  : 'text-gray-500 dark:text-gray-400 hover:text-red-500'
              )}
            >
              <ThumbsUp className={cn('w-4 h-4', comment.isLiked && 'fill-current')} />
              <span>{comment.likeCount || 0}</span>
            </button>

            <button
              onClick={() => setReplyTo(replyTo === comment.id ? null : comment.id)}
              className="text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors"
            >
              <Reply className="w-4 h-4 inline mr-1" />
              回复
            </button>

            <button className="text-gray-500 dark:text-gray-400 hover:text-orange-500 transition-colors">
              <Flag className="w-4 h-4 inline mr-1" />
              举报
            </button>
          </div>

          {/* 回复表单 */}
          {replyTo === comment.id && (
            <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              {!userId && allowGuests && (
                <div className="grid grid-cols-2 gap-3 mb-3">
                  <input
                    type="text"
                    placeholder="您的姓名"
                    value={guestName}
                    onChange={(e) => setGuestName(e.target.value)}
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                      bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                      focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="email"
                    placeholder="您的邮箱"
                    value={guestEmail}
                    onChange={(e) => setGuestEmail(e.target.value)}
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                      bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                      focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              )}
              
              <textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder={`回复 ${comment.userName || '匿名用户'}...`}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                  bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                  focus:ring-2 focus:ring-blue-500 focus:border-transparent
                  resize-none"
                rows={3}
              />
              
              <div className="flex justify-end gap-2 mt-3">
                <button
                  onClick={() => {
                    setReplyTo(null);
                    setReplyContent('');
                  }}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                >
                  取消
                </button>
                <button
                  onClick={() => submitComment(replyContent, comment.id)}
                  disabled={isSubmitting || !replyContent.trim()}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600
                    disabled:opacity-50 disabled:cursor-not-allowed
                    flex items-center gap-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                  发送回复
                </button>
              </div>
            </div>
          )}

          {/* 渲染回复 */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-4">
              {comment.replies.map(reply => renderComment(reply, true))}
            </div>
          )}
        </div>
      </div>
    </div>
    );
  };

  if (isLoading) {
    return (
      <div id="comments" className={cn('py-8', className)}>
        <div className="flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">加载评论中...</span>
        </div>
      </div>
    );
  }

  return (
    <div id="comments" className={cn('py-8', className)}>
      {/* 评论标题 */}
      <div className="flex items-center gap-3 mb-6">
        <MessageCircle className="w-6 h-6 text-gray-600 dark:text-gray-400" />
        <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          评论 ({comments.length})
        </h3>
      </div>

      {/* 发表评论表单 */}
      <div className="mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-xl">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          发表评论
        </h4>
        
        {!userId && allowGuests && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <input
              type="text"
              placeholder="您的姓名 *"
              value={guestName}
              onChange={(e) => setGuestName(e.target.value)}
              className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
                bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <input
              type="email"
              placeholder="您的邮箱 *"
              value={guestEmail}
              onChange={(e) => setGuestEmail(e.target.value)}
              className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
                bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        )}
        
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="写下您的评论..."
          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
            focus:ring-2 focus:ring-blue-500 focus:border-transparent
            resize-none"
          rows={4}
        />
        
        <div className="flex justify-end items-center mt-4">
          <button
            onClick={() => submitComment(newComment)}
            disabled={isSubmitting || !newComment.trim() || (!userId && (!guestName.trim() || !guestEmail.trim()))}
            className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600
              disabled:opacity-50 disabled:cursor-not-allowed
              flex items-center gap-2 transition-colors"
          >
            {isSubmitting ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
            发表评论
          </button>
        </div>
      </div>

      {/* 评论列表 */}
      <div className="space-y-6">
        {comments.length === 0 ? (
          <div className="text-center py-12">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400 text-lg">
              还没有评论，来发表第一个评论吧！
            </p>
          </div>
        ) : (
          comments.map(comment => renderComment(comment))
        )}
      </div>
    </div>
  );
}